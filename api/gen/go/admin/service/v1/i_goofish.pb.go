// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_goofish.proto

package servicev1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	v1 "kratos-admin/api/gen/go/goofish/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_goofish_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_goofish_proto_rawDesc = "" +
	"\n" +
	" admin/service/v1/i_goofish.proto\x12\x10admin.service.v1\x1a goofish/service/v1/goofish.proto\x1a\x1cgoogle/api/annotations.proto2\xeb\f\n" +
	"\n" +
	"GoofishApi\x12s\n" +
	"\x0fGetPlatformInfo\x12\x1f.goofish.v1.PlatformInfoRequest\x1a .goofish.v1.PlatformInfoResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/goofish/open/info\x12g\n" +
	"\vGetUserInfo\x12\x1b.goofish.v1.UserInfoRequest\x1a\x1c.goofish.v1.UserInfoResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/goofish/user/info\x12k\n" +
	"\fGetGoodsList\x12\x1c.goofish.v1.GoodsListRequest\x1a\x1d.goofish.v1.GoodsListResponse\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/goofish/goods/list\x12s\n" +
	"\x0eGetGoodsDetail\x12\x1e.goofish.v1.GoodsDetailRequest\x1a\x1f.goofish.v1.GoodsDetailResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/goofish/goods/detail\x12\xa9\x01\n" +
	"\x1bGetGoodsChangeSubscribeList\x12+.goofish.v1.GoodsChangeSubscribeListRequest\x1a,.goofish.v1.GoodsChangeSubscribeListResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/goofish/goods/change/subscribe/list\x12\x95\x01\n" +
	"\x14GoodsChangeSubscribe\x12'.goofish.v1.GoodsChangeSubscribeRequest\x1a(.goofish.v1.GoodsChangeSubscribeResponse\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/goofish/goods/change/subscribe\x12\x9d\x01\n" +
	"\x16GoodsChangeUnsubscribe\x12).goofish.v1.GoodsChangeUnsubscribeRequest\x1a*.goofish.v1.GoodsChangeUnsubscribeResponse\",\x82\xd3\xe4\x93\x02&:\x01*\"!/goofish/goods/change/unsubscribe\x12\x91\x01\n" +
	"\x13CreateRechargeOrder\x12&.goofish.v1.CreateRechargeOrderRequest\x1a'.goofish.v1.CreateRechargeOrderResponse\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/goofish/order/recharge/create\x12\x85\x01\n" +
	"\x0fCreateCardOrder\x12\".goofish.v1.CreateCardOrderRequest\x1a#.goofish.v1.CreateCardOrderResponse\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/goofish/order/purchase/create\x12s\n" +
	"\x0eGetOrderDetail\x12\x1e.goofish.v1.OrderDetailRequest\x1a\x1f.goofish.v1.OrderDetailResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/goofish/order/detail\x12\x92\x01\n" +
	"\rGoodsCallback\x12 .goofish.v1.GoodsCallbackRequest\x1a!.goofish.v1.GoodsCallbackResponse\"<\x82\xd3\xe4\x93\x026:\x01*\"1/api/open/callback/virtual/goods/notify/{token=*}\x12\x92\x01\n" +
	"\rOrderCallback\x12 .goofish.v1.OrderCallbackRequest\x1a!.goofish.v1.OrderCallbackResponse\"<\x82\xd3\xe4\x93\x026:\x01*\"1/api/open/callback/virtual/order/notify/{token=*}B\xbb\x01\n" +
	"\x14com.admin.service.v1B\rIGoofishProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_goofish_proto_goTypes = []any{
	(*v1.PlatformInfoRequest)(nil),              // 0: goofish.v1.PlatformInfoRequest
	(*v1.UserInfoRequest)(nil),                  // 1: goofish.v1.UserInfoRequest
	(*v1.GoodsListRequest)(nil),                 // 2: goofish.v1.GoodsListRequest
	(*v1.GoodsDetailRequest)(nil),               // 3: goofish.v1.GoodsDetailRequest
	(*v1.GoodsChangeSubscribeListRequest)(nil),  // 4: goofish.v1.GoodsChangeSubscribeListRequest
	(*v1.GoodsChangeSubscribeRequest)(nil),      // 5: goofish.v1.GoodsChangeSubscribeRequest
	(*v1.GoodsChangeUnsubscribeRequest)(nil),    // 6: goofish.v1.GoodsChangeUnsubscribeRequest
	(*v1.CreateRechargeOrderRequest)(nil),       // 7: goofish.v1.CreateRechargeOrderRequest
	(*v1.CreateCardOrderRequest)(nil),           // 8: goofish.v1.CreateCardOrderRequest
	(*v1.OrderDetailRequest)(nil),               // 9: goofish.v1.OrderDetailRequest
	(*v1.GoodsCallbackRequest)(nil),             // 10: goofish.v1.GoodsCallbackRequest
	(*v1.OrderCallbackRequest)(nil),             // 11: goofish.v1.OrderCallbackRequest
	(*v1.PlatformInfoResponse)(nil),             // 12: goofish.v1.PlatformInfoResponse
	(*v1.UserInfoResponse)(nil),                 // 13: goofish.v1.UserInfoResponse
	(*v1.GoodsListResponse)(nil),                // 14: goofish.v1.GoodsListResponse
	(*v1.GoodsDetailResponse)(nil),              // 15: goofish.v1.GoodsDetailResponse
	(*v1.GoodsChangeSubscribeListResponse)(nil), // 16: goofish.v1.GoodsChangeSubscribeListResponse
	(*v1.GoodsChangeSubscribeResponse)(nil),     // 17: goofish.v1.GoodsChangeSubscribeResponse
	(*v1.GoodsChangeUnsubscribeResponse)(nil),   // 18: goofish.v1.GoodsChangeUnsubscribeResponse
	(*v1.CreateRechargeOrderResponse)(nil),      // 19: goofish.v1.CreateRechargeOrderResponse
	(*v1.CreateCardOrderResponse)(nil),          // 20: goofish.v1.CreateCardOrderResponse
	(*v1.OrderDetailResponse)(nil),              // 21: goofish.v1.OrderDetailResponse
	(*v1.GoodsCallbackResponse)(nil),            // 22: goofish.v1.GoodsCallbackResponse
	(*v1.OrderCallbackResponse)(nil),            // 23: goofish.v1.OrderCallbackResponse
}
var file_admin_service_v1_i_goofish_proto_depIdxs = []int32{
	0,  // 0: admin.service.v1.GoofishApi.GetPlatformInfo:input_type -> goofish.v1.PlatformInfoRequest
	1,  // 1: admin.service.v1.GoofishApi.GetUserInfo:input_type -> goofish.v1.UserInfoRequest
	2,  // 2: admin.service.v1.GoofishApi.GetGoodsList:input_type -> goofish.v1.GoodsListRequest
	3,  // 3: admin.service.v1.GoofishApi.GetGoodsDetail:input_type -> goofish.v1.GoodsDetailRequest
	4,  // 4: admin.service.v1.GoofishApi.GetGoodsChangeSubscribeList:input_type -> goofish.v1.GoodsChangeSubscribeListRequest
	5,  // 5: admin.service.v1.GoofishApi.GoodsChangeSubscribe:input_type -> goofish.v1.GoodsChangeSubscribeRequest
	6,  // 6: admin.service.v1.GoofishApi.GoodsChangeUnsubscribe:input_type -> goofish.v1.GoodsChangeUnsubscribeRequest
	7,  // 7: admin.service.v1.GoofishApi.CreateRechargeOrder:input_type -> goofish.v1.CreateRechargeOrderRequest
	8,  // 8: admin.service.v1.GoofishApi.CreateCardOrder:input_type -> goofish.v1.CreateCardOrderRequest
	9,  // 9: admin.service.v1.GoofishApi.GetOrderDetail:input_type -> goofish.v1.OrderDetailRequest
	10, // 10: admin.service.v1.GoofishApi.GoodsCallback:input_type -> goofish.v1.GoodsCallbackRequest
	11, // 11: admin.service.v1.GoofishApi.OrderCallback:input_type -> goofish.v1.OrderCallbackRequest
	12, // 12: admin.service.v1.GoofishApi.GetPlatformInfo:output_type -> goofish.v1.PlatformInfoResponse
	13, // 13: admin.service.v1.GoofishApi.GetUserInfo:output_type -> goofish.v1.UserInfoResponse
	14, // 14: admin.service.v1.GoofishApi.GetGoodsList:output_type -> goofish.v1.GoodsListResponse
	15, // 15: admin.service.v1.GoofishApi.GetGoodsDetail:output_type -> goofish.v1.GoodsDetailResponse
	16, // 16: admin.service.v1.GoofishApi.GetGoodsChangeSubscribeList:output_type -> goofish.v1.GoodsChangeSubscribeListResponse
	17, // 17: admin.service.v1.GoofishApi.GoodsChangeSubscribe:output_type -> goofish.v1.GoodsChangeSubscribeResponse
	18, // 18: admin.service.v1.GoofishApi.GoodsChangeUnsubscribe:output_type -> goofish.v1.GoodsChangeUnsubscribeResponse
	19, // 19: admin.service.v1.GoofishApi.CreateRechargeOrder:output_type -> goofish.v1.CreateRechargeOrderResponse
	20, // 20: admin.service.v1.GoofishApi.CreateCardOrder:output_type -> goofish.v1.CreateCardOrderResponse
	21, // 21: admin.service.v1.GoofishApi.GetOrderDetail:output_type -> goofish.v1.OrderDetailResponse
	22, // 22: admin.service.v1.GoofishApi.GoodsCallback:output_type -> goofish.v1.GoodsCallbackResponse
	23, // 23: admin.service.v1.GoofishApi.OrderCallback:output_type -> goofish.v1.OrderCallbackResponse
	12, // [12:24] is the sub-list for method output_type
	0,  // [0:12] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_goofish_proto_init() }
func file_admin_service_v1_i_goofish_proto_init() {
	if File_admin_service_v1_i_goofish_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_goofish_proto_rawDesc), len(file_admin_service_v1_i_goofish_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_goofish_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_goofish_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_goofish_proto = out.File
	file_admin_service_v1_i_goofish_proto_goTypes = nil
	file_admin_service_v1_i_goofish_proto_depIdxs = nil
}
