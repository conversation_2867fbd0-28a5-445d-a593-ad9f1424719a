// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: admin/service/v1/i_goofish.proto

package servicev1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v1 "kratos-admin/api/gen/go/goofish/service/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationGoofishApiCreateCardOrder = "/admin.service.v1.GoofishApi/CreateCardOrder"
const OperationGoofishApiCreateRechargeOrder = "/admin.service.v1.GoofishApi/CreateRechargeOrder"
const OperationGoofishApiGetGoodsChangeSubscribeList = "/admin.service.v1.GoofishApi/GetGoodsChangeSubscribeList"
const OperationGoofishApiGetGoodsDetail = "/admin.service.v1.GoofishApi/GetGoodsDetail"
const OperationGoofishApiGetGoodsList = "/admin.service.v1.GoofishApi/GetGoodsList"
const OperationGoofishApiGetOrderDetail = "/admin.service.v1.GoofishApi/GetOrderDetail"
const OperationGoofishApiGetPlatformInfo = "/admin.service.v1.GoofishApi/GetPlatformInfo"
const OperationGoofishApiGetUserInfo = "/admin.service.v1.GoofishApi/GetUserInfo"
const OperationGoofishApiGoodsCallback = "/admin.service.v1.GoofishApi/GoodsCallback"
const OperationGoofishApiGoodsChangeSubscribe = "/admin.service.v1.GoofishApi/GoodsChangeSubscribe"
const OperationGoofishApiGoodsChangeUnsubscribe = "/admin.service.v1.GoofishApi/GoodsChangeUnsubscribe"
const OperationGoofishApiOrderCallback = "/admin.service.v1.GoofishApi/OrderCallback"

type GoofishApiHTTPServer interface {
	// CreateCardOrder 创建卡密订单
	CreateCardOrder(context.Context, *v1.CreateCardOrderRequest) (*v1.CreateCardOrderResponse, error)
	// CreateRechargeOrder 创建直充订单
	CreateRechargeOrder(context.Context, *v1.CreateRechargeOrderRequest) (*v1.CreateRechargeOrderResponse, error)
	// GetGoodsChangeSubscribeList 查询商品订阅列表
	GetGoodsChangeSubscribeList(context.Context, *v1.GoodsChangeSubscribeListRequest) (*v1.GoodsChangeSubscribeListResponse, error)
	// GetGoodsDetail 查询商品详情
	GetGoodsDetail(context.Context, *v1.GoodsDetailRequest) (*v1.GoodsDetailResponse, error)
	// GetGoodsList 查询商品列表
	GetGoodsList(context.Context, *v1.GoodsListRequest) (*v1.GoodsListResponse, error)
	// GetOrderDetail 查询订单详情
	GetOrderDetail(context.Context, *v1.OrderDetailRequest) (*v1.OrderDetailResponse, error)
	// GetPlatformInfo 查询平台信息
	GetPlatformInfo(context.Context, *v1.PlatformInfoRequest) (*v1.PlatformInfoResponse, error)
	// GetUserInfo 查询商户信息
	GetUserInfo(context.Context, *v1.UserInfoRequest) (*v1.UserInfoResponse, error)
	// GoodsCallback 商品回调通知
	GoodsCallback(context.Context, *v1.GoodsCallbackRequest) (*v1.GoodsCallbackResponse, error)
	// GoodsChangeSubscribe 订阅商品变更通知
	GoodsChangeSubscribe(context.Context, *v1.GoodsChangeSubscribeRequest) (*v1.GoodsChangeSubscribeResponse, error)
	// GoodsChangeUnsubscribe 取消商品变更通知
	GoodsChangeUnsubscribe(context.Context, *v1.GoodsChangeUnsubscribeRequest) (*v1.GoodsChangeUnsubscribeResponse, error)
	// OrderCallback 订单回调通知
	OrderCallback(context.Context, *v1.OrderCallbackRequest) (*v1.OrderCallbackResponse, error)
}

func RegisterGoofishApiHTTPServer(s *http.Server, srv GoofishApiHTTPServer) {
	r := s.Route("/")
	r.POST("/goofish/open/info", _GoofishApi_GetPlatformInfo0_HTTP_Handler(srv))
	r.POST("/goofish/user/info", _GoofishApi_GetUserInfo0_HTTP_Handler(srv))
	r.POST("/goofish/goods/list", _GoofishApi_GetGoodsList0_HTTP_Handler(srv))
	r.POST("/goofish/goods/detail", _GoofishApi_GetGoodsDetail0_HTTP_Handler(srv))
	r.POST("/goofish/goods/change/subscribe/list", _GoofishApi_GetGoodsChangeSubscribeList0_HTTP_Handler(srv))
	r.POST("/goofish/goods/change/subscribe", _GoofishApi_GoodsChangeSubscribe0_HTTP_Handler(srv))
	r.POST("/goofish/goods/change/unsubscribe", _GoofishApi_GoodsChangeUnsubscribe0_HTTP_Handler(srv))
	r.POST("/goofish/order/recharge/create", _GoofishApi_CreateRechargeOrder0_HTTP_Handler(srv))
	r.POST("/goofish/order/purchase/create", _GoofishApi_CreateCardOrder0_HTTP_Handler(srv))
	r.POST("/goofish/order/detail", _GoofishApi_GetOrderDetail0_HTTP_Handler(srv))
	r.POST("/api/open/callback/virtual/goods/notify/{token:.*}", _GoofishApi_GoodsCallback0_HTTP_Handler(srv))
	r.POST("/api/open/callback/virtual/order/notify/{token:.*}", _GoofishApi_OrderCallback0_HTTP_Handler(srv))
}

func _GoofishApi_GetPlatformInfo0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PlatformInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetPlatformInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPlatformInfo(ctx, req.(*v1.PlatformInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.PlatformInfoResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetUserInfo0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.UserInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserInfo(ctx, req.(*v1.UserInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.UserInfoResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetGoodsList0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.GoodsListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetGoodsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsList(ctx, req.(*v1.GoodsListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.GoodsListResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetGoodsDetail0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.GoodsDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetGoodsDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsDetail(ctx, req.(*v1.GoodsDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.GoodsDetailResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetGoodsChangeSubscribeList0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.GoodsChangeSubscribeListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetGoodsChangeSubscribeList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsChangeSubscribeList(ctx, req.(*v1.GoodsChangeSubscribeListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.GoodsChangeSubscribeListResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GoodsChangeSubscribe0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.GoodsChangeSubscribeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGoodsChangeSubscribe)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsChangeSubscribe(ctx, req.(*v1.GoodsChangeSubscribeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.GoodsChangeSubscribeResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GoodsChangeUnsubscribe0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.GoodsChangeUnsubscribeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGoodsChangeUnsubscribe)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsChangeUnsubscribe(ctx, req.(*v1.GoodsChangeUnsubscribeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.GoodsChangeUnsubscribeResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_CreateRechargeOrder0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.CreateRechargeOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiCreateRechargeOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateRechargeOrder(ctx, req.(*v1.CreateRechargeOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.CreateRechargeOrderResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_CreateCardOrder0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.CreateCardOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiCreateCardOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateCardOrder(ctx, req.(*v1.CreateCardOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.CreateCardOrderResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GetOrderDetail0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.OrderDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGetOrderDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderDetail(ctx, req.(*v1.OrderDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.OrderDetailResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_GoodsCallback0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.GoodsCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiGoodsCallback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsCallback(ctx, req.(*v1.GoodsCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.GoodsCallbackResponse)
		return ctx.Result(200, reply)
	}
}

func _GoofishApi_OrderCallback0_HTTP_Handler(srv GoofishApiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.OrderCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationGoofishApiOrderCallback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OrderCallback(ctx, req.(*v1.OrderCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.OrderCallbackResponse)
		return ctx.Result(200, reply)
	}
}

type GoofishApiHTTPClient interface {
	CreateCardOrder(ctx context.Context, req *v1.CreateCardOrderRequest, opts ...http.CallOption) (rsp *v1.CreateCardOrderResponse, err error)
	CreateRechargeOrder(ctx context.Context, req *v1.CreateRechargeOrderRequest, opts ...http.CallOption) (rsp *v1.CreateRechargeOrderResponse, err error)
	GetGoodsChangeSubscribeList(ctx context.Context, req *v1.GoodsChangeSubscribeListRequest, opts ...http.CallOption) (rsp *v1.GoodsChangeSubscribeListResponse, err error)
	GetGoodsDetail(ctx context.Context, req *v1.GoodsDetailRequest, opts ...http.CallOption) (rsp *v1.GoodsDetailResponse, err error)
	GetGoodsList(ctx context.Context, req *v1.GoodsListRequest, opts ...http.CallOption) (rsp *v1.GoodsListResponse, err error)
	GetOrderDetail(ctx context.Context, req *v1.OrderDetailRequest, opts ...http.CallOption) (rsp *v1.OrderDetailResponse, err error)
	GetPlatformInfo(ctx context.Context, req *v1.PlatformInfoRequest, opts ...http.CallOption) (rsp *v1.PlatformInfoResponse, err error)
	GetUserInfo(ctx context.Context, req *v1.UserInfoRequest, opts ...http.CallOption) (rsp *v1.UserInfoResponse, err error)
	GoodsCallback(ctx context.Context, req *v1.GoodsCallbackRequest, opts ...http.CallOption) (rsp *v1.GoodsCallbackResponse, err error)
	GoodsChangeSubscribe(ctx context.Context, req *v1.GoodsChangeSubscribeRequest, opts ...http.CallOption) (rsp *v1.GoodsChangeSubscribeResponse, err error)
	GoodsChangeUnsubscribe(ctx context.Context, req *v1.GoodsChangeUnsubscribeRequest, opts ...http.CallOption) (rsp *v1.GoodsChangeUnsubscribeResponse, err error)
	OrderCallback(ctx context.Context, req *v1.OrderCallbackRequest, opts ...http.CallOption) (rsp *v1.OrderCallbackResponse, err error)
}

type GoofishApiHTTPClientImpl struct {
	cc *http.Client
}

func NewGoofishApiHTTPClient(client *http.Client) GoofishApiHTTPClient {
	return &GoofishApiHTTPClientImpl{client}
}

func (c *GoofishApiHTTPClientImpl) CreateCardOrder(ctx context.Context, in *v1.CreateCardOrderRequest, opts ...http.CallOption) (*v1.CreateCardOrderResponse, error) {
	var out v1.CreateCardOrderResponse
	pattern := "/goofish/order/purchase/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiCreateCardOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) CreateRechargeOrder(ctx context.Context, in *v1.CreateRechargeOrderRequest, opts ...http.CallOption) (*v1.CreateRechargeOrderResponse, error) {
	var out v1.CreateRechargeOrderResponse
	pattern := "/goofish/order/recharge/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiCreateRechargeOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetGoodsChangeSubscribeList(ctx context.Context, in *v1.GoodsChangeSubscribeListRequest, opts ...http.CallOption) (*v1.GoodsChangeSubscribeListResponse, error) {
	var out v1.GoodsChangeSubscribeListResponse
	pattern := "/goofish/goods/change/subscribe/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetGoodsChangeSubscribeList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetGoodsDetail(ctx context.Context, in *v1.GoodsDetailRequest, opts ...http.CallOption) (*v1.GoodsDetailResponse, error) {
	var out v1.GoodsDetailResponse
	pattern := "/goofish/goods/detail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetGoodsDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetGoodsList(ctx context.Context, in *v1.GoodsListRequest, opts ...http.CallOption) (*v1.GoodsListResponse, error) {
	var out v1.GoodsListResponse
	pattern := "/goofish/goods/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetGoodsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetOrderDetail(ctx context.Context, in *v1.OrderDetailRequest, opts ...http.CallOption) (*v1.OrderDetailResponse, error) {
	var out v1.OrderDetailResponse
	pattern := "/goofish/order/detail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetOrderDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetPlatformInfo(ctx context.Context, in *v1.PlatformInfoRequest, opts ...http.CallOption) (*v1.PlatformInfoResponse, error) {
	var out v1.PlatformInfoResponse
	pattern := "/goofish/open/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetPlatformInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GetUserInfo(ctx context.Context, in *v1.UserInfoRequest, opts ...http.CallOption) (*v1.UserInfoResponse, error) {
	var out v1.UserInfoResponse
	pattern := "/goofish/user/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGetUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GoodsCallback(ctx context.Context, in *v1.GoodsCallbackRequest, opts ...http.CallOption) (*v1.GoodsCallbackResponse, error) {
	var out v1.GoodsCallbackResponse
	pattern := "/api/open/callback/virtual/goods/notify/{token:.*}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGoodsCallback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GoodsChangeSubscribe(ctx context.Context, in *v1.GoodsChangeSubscribeRequest, opts ...http.CallOption) (*v1.GoodsChangeSubscribeResponse, error) {
	var out v1.GoodsChangeSubscribeResponse
	pattern := "/goofish/goods/change/subscribe"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGoodsChangeSubscribe))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) GoodsChangeUnsubscribe(ctx context.Context, in *v1.GoodsChangeUnsubscribeRequest, opts ...http.CallOption) (*v1.GoodsChangeUnsubscribeResponse, error) {
	var out v1.GoodsChangeUnsubscribeResponse
	pattern := "/goofish/goods/change/unsubscribe"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiGoodsChangeUnsubscribe))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *GoofishApiHTTPClientImpl) OrderCallback(ctx context.Context, in *v1.OrderCallbackRequest, opts ...http.CallOption) (*v1.OrderCallbackResponse, error) {
	var out v1.OrderCallbackResponse
	pattern := "/api/open/callback/virtual/order/notify/{token:.*}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationGoofishApiOrderCallback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
