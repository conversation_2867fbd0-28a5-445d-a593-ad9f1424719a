{"openapi": "3.0.3", "info": {"title": "GoofishApi API", "description": "goofish API 路由配置，字段与 Apifox 文档保持一致", "version": "0.0.1"}, "paths": {"/api/open/callback/virtual/goods/notify/*": {"post": {"tags": ["GoofishApi"], "description": "商品回调通知", "operationId": "GoofishApi_GoodsCallback", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsCallbackRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsCallbackResponse"}}}}}}}, "/api/open/callback/virtual/order/notify/*": {"post": {"tags": ["GoofishApi"], "description": "订单回调通知", "operationId": "GoofishApi_OrderCallback", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCallbackRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCallbackResponse"}}}}}}}, "/goofish/goods/change/subscribe": {"post": {"tags": ["GoofishApi"], "description": "订阅商品变更通知", "operationId": "GoofishApi_GoodsChangeSubscribe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsChangeSubscribeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsChangeSubscribeResponse"}}}}}}}, "/goofish/goods/change/subscribe/list": {"post": {"tags": ["GoofishApi"], "description": "查询商品订阅列表", "operationId": "GoofishApi_GetGoodsChangeSubscribeList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsChangeSubscribeListRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsChangeSubscribeListResponse"}}}}}}}, "/goofish/goods/change/unsubscribe": {"post": {"tags": ["GoofishApi"], "description": "取消商品变更通知", "operationId": "GoofishApi_GoodsChangeUnsubscribe", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsChangeUnsubscribeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsChangeUnsubscribeResponse"}}}}}}}, "/goofish/goods/detail": {"post": {"tags": ["GoofishApi"], "description": "查询商品详情", "operationId": "GoofishApi_GetGoodsDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsDetailRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsDetailResponse"}}}}}}}, "/goofish/goods/list": {"post": {"tags": ["GoofishApi"], "description": "查询商品列表", "operationId": "GoofishApi_GetGoodsList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsListRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoodsListResponse"}}}}}}}, "/goofish/open/info": {"post": {"tags": ["GoofishApi"], "description": "查询平台信息", "operationId": "GoofishApi_GetPlatformInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlatformInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlatformInfoResponse"}}}}}}}, "/goofish/order/detail": {"post": {"tags": ["GoofishApi"], "description": "查询订单详情", "operationId": "GoofishApi_GetOrderDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDetailRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDetailResponse"}}}}}}}, "/goofish/order/purchase/create": {"post": {"tags": ["GoofishApi"], "description": "创建卡密订单", "operationId": "GoofishApi_CreateCardOrder", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCardOrderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCardOrderResponse"}}}}}}}, "/goofish/order/recharge/create": {"post": {"tags": ["GoofishApi"], "description": "创建直充订单", "operationId": "GoofishApi_CreateRechargeOrder", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRechargeOrderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRechargeOrderResponse"}}}}}}}, "/goofish/user/info": {"post": {"tags": ["GoofishApi"], "description": "查询商户信息", "operationId": "GoofishApi_GetUserInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoResponse"}}}}}}}}, "components": {"schemas": {"BizContent": {"type": "object", "properties": {"account": {"type": "string"}, "gameName": {"type": "string"}, "gameRole": {"type": "string"}, "gameArea": {"type": "string"}, "gameServer": {"type": "string"}, "buyerIp": {"type": "string"}, "buyerArea": {"type": "string"}}, "description": "订单相关"}, "CardItem": {"type": "object", "properties": {"cardNo": {"type": "string"}, "cardPwd": {"type": "string"}}}, "CardOrderData": {"type": "object", "properties": {"orderNo": {"type": "string"}, "outOrderNo": {"type": "string"}, "orderStatus": {"type": "integer", "format": "int32"}, "orderAmount": {"type": "string"}, "orderTime": {"type": "string"}, "endTime": {"type": "string"}, "cardItems": {"type": "array", "items": {"$ref": "#/components/schemas/CardItem"}}, "remark": {"type": "string"}}}, "CreateCardOrderRequest": {"type": "object", "properties": {"orderNo": {"type": "string"}, "goodsNo": {"type": "string"}, "buyQuantity": {"type": "integer", "format": "int32"}, "maxAmount": {"type": "string"}, "notifyUrl": {"type": "string"}, "bizOrderNo": {"type": "string"}}, "description": "创建卡密订单"}, "CreateCardOrderResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/CardOrderData"}}}, "CreateRechargeOrderRequest": {"type": "object", "properties": {"orderNo": {"type": "string"}, "goodsNo": {"type": "string"}, "bizContent": {"$ref": "#/components/schemas/BizContent"}, "buyQuantity": {"type": "integer", "format": "int32"}, "maxAmount": {"type": "string"}, "notifyUrl": {"type": "string"}, "bizOrderNo": {"type": "string"}}, "description": "创建直充订单"}, "CreateRechargeOrderResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/RechargeOrderData"}}}, "GoodsCallbackItem": {"type": "object", "properties": {"goodsNo": {"type": "string"}, "goodsType": {"type": "integer", "format": "int32"}, "price": {"type": "string"}, "stock": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "changeTime": {"type": "string"}}, "description": "商品回调通知"}, "GoodsCallbackRequest": {"type": "object", "properties": {"token": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsCallbackItem"}}}}, "GoodsCallbackResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}}, "GoodsChangeSubscribeListData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsChangeSubscribeListItem"}}, "count": {"type": "integer", "format": "int32"}}}, "GoodsChangeSubscribeListItem": {"type": "object", "properties": {"goodsType": {"type": "integer", "format": "int32"}, "goodsNo": {"type": "string"}, "subscribeTime": {"type": "string"}, "token": {"type": "string"}, "notifyUrl": {"type": "string"}}}, "GoodsChangeSubscribeListRequest": {"type": "object", "properties": {"goodsType": {"type": "integer", "format": "int32"}, "goodsNo": {"type": "string"}, "pageNo": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "description": "查询商品订阅列表"}, "GoodsChangeSubscribeListResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/GoodsChangeSubscribeListData"}}}, "GoodsChangeSubscribeRequest": {"type": "object", "properties": {"goodsType": {"type": "integer", "format": "int32"}, "goodsNo": {"type": "string"}, "token": {"type": "string"}, "notifyUrl": {"type": "string"}}, "description": "订阅商品变更通知"}, "GoodsChangeSubscribeResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}}, "GoodsChangeUnsubscribeRequest": {"type": "object", "properties": {"goodsType": {"type": "integer", "format": "int32"}, "goodsNo": {"type": "string"}, "token": {"type": "string"}}, "description": "取消商品变更通知"}, "GoodsChangeUnsubscribeResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}}, "GoodsDetail": {"type": "object", "properties": {"goodsNo": {"type": "string"}, "goodsType": {"type": "integer", "format": "int32"}, "goodsName": {"type": "string"}, "price": {"type": "string"}, "stock": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "updateTime": {"type": "string"}, "template": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsTemplate"}}}, "description": "商品详情"}, "GoodsDetailRequest": {"type": "object", "properties": {"goodsType": {"type": "integer", "format": "int32"}, "goodsNo": {"type": "string"}}, "description": "查询商品详情"}, "GoodsDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/GoodsDetail"}}}, "GoodsListData": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsDetail"}}, "count": {"type": "integer", "format": "int32"}}}, "GoodsListRequest": {"type": "object", "properties": {"keyword": {"type": "string"}, "goodsType": {"type": "integer", "format": "int32"}, "pageNo": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "description": "查询商品列表"}, "GoodsListResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/GoodsListData"}}}, "GoodsTemplate": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "desc": {"type": "string"}, "check": {"type": "integer", "format": "int32"}}, "description": "商品详情模板"}, "OrderCallbackRequest": {"type": "object", "properties": {"token": {"type": "string"}, "orderType": {"type": "integer", "format": "int32"}, "orderNo": {"type": "string"}, "outOrderNo": {"type": "string"}, "orderStatus": {"type": "integer", "format": "int32"}, "endTime": {"type": "string"}, "cardItems": {"type": "array", "items": {"$ref": "#/components/schemas/CardItem"}}, "remark": {"type": "string"}}, "description": "订单回调通知"}, "OrderCallbackResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}}, "OrderDetailData": {"type": "object", "properties": {"orderType": {"type": "integer", "format": "int32"}, "orderNo": {"type": "string"}, "outOrderNo": {"type": "string"}, "orderStatus": {"type": "integer", "format": "int32"}, "orderAmount": {"type": "string"}, "goodsNo": {"type": "string"}, "goodsName": {"type": "string"}, "buyQuantity": {"type": "integer", "format": "int32"}, "orderTime": {"type": "string"}, "endTime": {"type": "string"}, "bizContent": {"$ref": "#/components/schemas/BizContent"}, "cardItems": {"type": "array", "items": {"$ref": "#/components/schemas/CardItem"}}, "remark": {"type": "string"}}}, "OrderDetailRequest": {"type": "object", "properties": {"orderType": {"type": "integer", "format": "int32"}, "orderNo": {"type": "string"}, "outOrderNo": {"type": "string"}}, "description": "查询订单详情"}, "OrderDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/OrderDetailData"}}}, "PlatformInfoData": {"type": "object", "properties": {"appId": {"type": "string"}}}, "PlatformInfoRequest": {"type": "object", "properties": {}, "description": "查询平台信息"}, "PlatformInfoResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/PlatformInfoData"}}}, "RechargeOrderData": {"type": "object", "properties": {"orderNo": {"type": "string"}, "outOrderNo": {"type": "string"}, "orderStatus": {"type": "integer", "format": "int32"}, "orderAmount": {"type": "string"}, "goodsName": {"type": "string"}, "orderTime": {"type": "string"}, "endTime": {"type": "string"}, "remark": {"type": "string"}}}, "UserInfoData": {"type": "object", "properties": {"balance": {"type": "string"}}}, "UserInfoRequest": {"type": "object", "properties": {}, "description": "查询商户信息"}, "UserInfoResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/UserInfoData"}}}}}, "tags": [{"name": "GoofishApi"}]}