syntax = "proto3";

package admin.service.v1;

import "goofish/service/v1/goofish.proto";
import "google/api/annotations.proto";

// goofish API 路由配置，字段与 Apifox 文档保持一致
service GoofishApi {
  // 查询平台信息
  rpc GetPlatformInfo(goofish.v1.PlatformInfoRequest) returns (goofish.v1.PlatformInfoResponse) {
    option (google.api.http) = {
      post: "/goofish/open/info"
      body: "*"
    };
  }

  // 查询商户信息
  rpc GetUserInfo(goofish.v1.UserInfoRequest) returns (goofish.v1.UserInfoResponse) {
    option (google.api.http) = {
      post: "/goofish/user/info"
      body: "*"
    };
  }

  // 查询商品列表
  rpc GetGoodsList(goofish.v1.GoodsListRequest) returns (goofish.v1.GoodsListResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/list"
      body: "*"
    };
  }

  // 查询商品详情
  rpc GetGoodsDetail(goofish.v1.GoodsDetailRequest) returns (goofish.v1.GoodsDetailResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/detail"
      body: "*"
    };
  }

  // 查询商品订阅列表
  rpc GetGoodsChangeSubscribeList(goofish.v1.GoodsChangeSubscribeListRequest) returns (goofish.v1.GoodsChangeSubscribeListResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/change/subscribe/list"
      body: "*"
    };
  }

  // 订阅商品变更通知
  rpc GoodsChangeSubscribe(goofish.v1.GoodsChangeSubscribeRequest) returns (goofish.v1.GoodsChangeSubscribeResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/change/subscribe"
      body: "*"
    };
  }

  // 取消商品变更通知
  rpc GoodsChangeUnsubscribe(goofish.v1.GoodsChangeUnsubscribeRequest) returns (goofish.v1.GoodsChangeUnsubscribeResponse) {
    option (google.api.http) = {
      post: "/goofish/goods/change/unsubscribe"
      body: "*"
    };
  }

  // 创建直充订单
  rpc CreateRechargeOrder(goofish.v1.CreateRechargeOrderRequest) returns (goofish.v1.CreateRechargeOrderResponse) {
    option (google.api.http) = {
      post: "/goofish/order/recharge/create"
      body: "*"
    };
  }

  // 创建卡密订单
  rpc CreateCardOrder(goofish.v1.CreateCardOrderRequest) returns (goofish.v1.CreateCardOrderResponse) {
    option (google.api.http) = {
      post: "/goofish/order/purchase/create"
      body: "*"
    };
  }

  // 查询订单详情
  rpc GetOrderDetail(goofish.v1.OrderDetailRequest) returns (goofish.v1.OrderDetailResponse) {
    option (google.api.http) = {
      post: "/goofish/order/detail"
      body: "*"
    };
  }

  // 商品回调通知
  rpc GoodsCallback(goofish.v1.GoodsCallbackRequest) returns (goofish.v1.GoodsCallbackResponse) {
    option (google.api.http) = {
      post: "/api/open/callback/virtual/goods/notify/{token=*}"
      body: "*"
    };
  }

  // 订单回调通知
  rpc OrderCallback(goofish.v1.OrderCallbackRequest) returns (goofish.v1.OrderCallbackResponse) {
    option (google.api.http) = {
      post: "/api/open/callback/virtual/order/notify/{token=*}"
      body: "*"
    };
  }
}
